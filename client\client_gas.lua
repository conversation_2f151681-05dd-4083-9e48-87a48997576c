local customGasPumps = {}
-----------------------------------------------------------------------------------------------------------------------------------------
-- Threads
-----------------------------------------------------------------------------------------------------------------------------------------

-- Thread to detect near fuel pumps (DISABLED - using interact system instead)
function createGasMarkersThread()
    -- This function is now disabled as we're using the interact system instead
end

function createGasTargetsThread()
    -- This function is now disabled as we're using the interact system instead
end

function openFuelUICallback()
    local ped = PlayerPedId()
    local playerCoords = GetEntityCoords(ped)
    local pump, pumpModel = GetClosestPump(playerCoords, false)
    if pump then
        clientOpenUI(pump, pumpModel, false)
    else
        exports['lc_utils']:notify("error", Utils.translate("pump_not_found"))
    end
end

-- Function to check if a pump is in an active gas station from config
local function isPumpInActiveGasStation(coords)
    -- If PlayerOwnedGasStations is not enabled, all pumps are active
    if not Config.PlayerOwnedGasStations or not Config.PlayerOwnedGasStations.enabled then
        return true
    end
    
    -- Check if the pump is in any of the active gas stations defined in config
    for stationId, station in pairs(Config.PlayerOwnedGasStations.gasStations) do
        local stationCoords = station.vector
        local radius = station.radius or 30.0
        
        -- Calculate distance between pump and gas station center
        local distance = #(coords - stationCoords)
        
        -- If pump is within the radius of an active gas station, it's active
        if distance <= radius then
            return true
        end
    end
    
    -- Pump is not in any active gas station
    return false
end

function createCustomPumpModelsThread()
    for _, pumpConfig in pairs(Config.CustomGasPumpLocations) do
        local coords = vector3(pumpConfig.location.x, pumpConfig.location.y, pumpConfig.location.z)
        
        -- Only create pumps that are in active gas stations
        if isPumpInActiveGasStation(coords) then
            RequestModel(pumpConfig.prop)

            while not HasModelLoaded(pumpConfig.prop) do
                Wait(50)
            end

            local heading = pumpConfig.location.w + 180.0
            local gasPump = CreateObject(pumpConfig.prop, pumpConfig.location.x, pumpConfig.location.y, pumpConfig.location.z, false, true, true)
            SetEntityHeading(gasPump, heading)
            FreezeEntityPosition(gasPump, true)
            table.insert(customGasPumps, gasPump)
        end
    end
end

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end

    deleteAllCustomGasPumps()
end)

function deleteAllCustomGasPumps()
    for k, v in ipairs(customGasPumps) do
        DeleteEntity(v)
    end
end

-----------------------------------------------------------------------------------------------------------------------------------------
-- Jerry Cans
-----------------------------------------------------------------------------------------------------------------------------------------

-- Thread to handle the fuel consumption
function createJerryCanThread()
    CreateThread(function()
        while true do
            Wait(1000)
            local ped = PlayerPedId()
            if not IsPedInAnyVehicle(ped, false) and GetSelectedPedWeapon(ped) == JERRY_CAN_HASH then
                refuelLoop(true)
            end
        end
    end)
end

-- Code to save jerry can ammo in any inventory
local currentWeaponData
function updateWeaponAmmo(ammo)
    ammo = math.floor(ammo) -- This is needed or some inventories will break

    if currentWeaponData and currentWeaponData.info and currentWeaponData.info.ammo then
        currentWeaponData.info.ammo = ammo
    end

    TriggerServerEvent('ox_inventory:updateWeapon', "ammo", ammo)
    TriggerServerEvent("weapons:server:UpdateWeaponAmmo", currentWeaponData, ammo)
    TriggerServerEvent("qb-weapons:server:UpdateWeaponAmmo", currentWeaponData, ammo)

    -- Update weapon ammo

    local ped = PlayerPedId()
    SetPedAmmo(ped, JERRY_CAN_HASH, ammo)
end

AddEventHandler('weapons:client:SetCurrentWeapon', function(data, bool)
    if bool ~= false then
        currentWeaponData = data
    else
        currentWeaponData = {}
    end
end)

AddEventHandler('qb-weapons:client:SetCurrentWeapon', function(data, bool)
    if bool ~= false then
        currentWeaponData = data
    else
        currentWeaponData = {}
    end
end)

-- Get jerry can ammo by metadata
function getJerryCanAmmo()
    local ped = PlayerPedId()
    local ammo = GetAmmoInPedWeapon(ped, JERRY_CAN_HASH)

    -- If ammo is 0, check if we have metadata from inventory
    if ammo <= 0 and currentWeaponData and currentWeaponData.info and currentWeaponData.info.ammo then
        -- Return ammo from weapon data
        return currentWeaponData.info.ammo
    end

    -- If ammo is still 0, set a default value to allow refueling
    if ammo <= 0 then
        ammo = 25 -- Default jerry can capacity
        SetPedAmmo(ped, JERRY_CAN_HASH, ammo)
    end

    -- Return ammo from ped weapon
    return ammo
end