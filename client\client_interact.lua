-- LC_Fuel - Interaction System
-- This file handles the integration with the interact export

local interactionPoints = {}
local isRegistering = false
local registeredInteractIds = {} -- Track registered interact IDs to prevent duplicates

-- Function to convert coordinates to interact ID format
local function coordsToInteractId(coords)
    return string.format('%s_%s_%s',
        math.floor(coords.x * 1000),
        math.floor(coords.y * 1000),
        math.floor(coords.z * 1000))
end

-- Helper function to create unique point IDs
local function createUniquePointId(prefix, entityId, coords)
    return prefix .. entityId .. '_' .. math.floor(coords.x) .. '_' .. math.floor(coords.y) .. '_' .. math.floor(coords.z)
end

-- Helper function to check if a point is the closest interaction point
local function isClosestInteractionPoint(point, playerPos, points)
    -- Get distance to this point
    local thisDistance = #(point.coords - playerPos)

    -- If we're too far from this point, don't show it
    if thisDistance > (point.distance or 2.0) then
        return false
    end

    -- Check if there's any closer point of the same type (gas or electric)
    for _, otherPoint in ipairs(points) do
        -- Only compare with points of the same type (gas vs gas, electric vs electric)
        if otherPoint ~= point and otherPoint.isElectric == point.isElectric then
            local otherDistance = #(otherPoint.coords - playerPos)
            -- If another point of the same type is closer, don't show this one
            if otherDistance < thisDistance then
                return false
            end
        end
    end

    return true
end

-- Function to check if a pump is in an active gas station from config
local function isPumpInActiveGasStation(coords, isElectric)
    -- If PlayerOwnedGasStations is not enabled, all pumps are active
    if not Config.PlayerOwnedGasStations or not Config.PlayerOwnedGasStations.enabled then
        return true
    end

    -- Always allow electric chargers to be active
    if isElectric then
        return true
    end

    -- Check if the pump is in any of the active gas stations defined in config
    for stationId, station in pairs(Config.PlayerOwnedGasStations.gasStations) do
        local stationCoords = station.vector
        local radius = station.radius or 30.0

        -- Calculate distance between pump and gas station center
        local distance = #(coords - stationCoords)

        -- If pump is within the radius of an active gas station, it's active
        if distance <= radius then
            return true
        end
    end

    -- Pump is not in any active gas station
    return false  -- Return false to hide pumps/chargers that are not in any active gas station
end

-- Function to create interaction points for gas pumps
function createGasInteractionPoints()
    -- Create a new table for gas pump interaction points
    local gasPoints = {}

    -- Get all gas pump models
    local pumpModels = {}
    for _, v in pairs(Config.GasPumpProps) do
        table.insert(pumpModels, v.prop)
    end

    -- Find all gas pumps in the world
    for _, pumpModel in ipairs(pumpModels) do
        local objects = GetGamePool('CObject')

        for _, object in ipairs(objects) do
            if GetEntityModel(object) == GetHashKey(pumpModel) then
                local coords = GetEntityCoords(object)

                -- Only add interaction points for pumps in active gas stations
                if isPumpInActiveGasStation(coords, false) then
                    local id = "gas_pump_" .. object

                    -- Add to gas points
                    table.insert(gasPoints, {
                        id = id,
                        coords = coords,
                        label = Utils.translate('target.open_refuel'),
                        icon = "fas fa-gas-pump",
                        distance = 2.0,
                        event = "lc_fuel:interactWithGasPump",
                        entityId = object,
                        pumpModel = pumpModel,
                        isElectric = false
                    })
                end
            end
        end
    end

    -- Remove existing gas points
    local removedCount = 0
    for i = #interactionPoints, 1, -1 do
        if interactionPoints[i] and not interactionPoints[i].isElectric then
            table.remove(interactionPoints, i)
            removedCount = removedCount + 1
        end
    end

    -- Add new gas points
    for _, point in ipairs(gasPoints) do
        table.insert(interactionPoints, point)
    end
end

-- Function to create interaction points for electric chargers
function createElectricInteractionPoints()
    if not Config.Electric.enabled then
        return
    end

    -- Create a new table for electric charger interaction points
    local electricPoints = {}

    -- First, try to find electric chargers in the world
    local chargerModels = {}
    for _, v in pairs(Config.Electric.chargersProps) do
        table.insert(chargerModels, v.prop)
    end

    -- Find all electric chargers in the world
    for _, chargerModel in ipairs(chargerModels) do
        local objects = GetGamePool('CObject')

        for _, object in ipairs(objects) do
            if GetEntityModel(object) == GetHashKey(chargerModel) then
                local coords = GetEntityCoords(object)

                -- Only add interaction points for chargers in active gas stations
                if isPumpInActiveGasStation(coords, true) then
                    local id = "electric_charger_" .. object

                    -- Add to temporary list
                    table.insert(electricPoints, {
                        id = id,
                        coords = coords,
                        label = Utils.translate('target.open_recharge'),
                        icon = "fas fa-plug",
                        distance = 2.0,
                        event = "lc_fuel:interactWithElectricCharger",
                        entityId = object,
                        pumpModel = chargerModel,
                        isElectric = true
                    })
                end
            end
        end
    end

    -- Then, add electric chargers from config locations
    for _, chargerLocation in ipairs(Config.Electric.chargersLocation) do
        -- Check if the charger is already spawned
        local chargerExists = false
        for _, point in ipairs(electricPoints) do
            if #(point.coords - vector3(chargerLocation.location.x, chargerLocation.location.y, chargerLocation.location.z)) < 1.0 then
                chargerExists = true
                break
            end
        end

        -- If the charger doesn't exist, add it from the config
        if not chargerExists then
            local coords = vector3(chargerLocation.location.x, chargerLocation.location.y, chargerLocation.location.z)

            -- Only add interaction points for chargers in active gas stations
            if isPumpInActiveGasStation(coords, true) then
                local id = "electric_charger_config_" .. coords.x .. coords.y .. coords.z

                -- Add to temporary list
                table.insert(electricPoints, {
                    id = id,
                    coords = coords,
                    label = Utils.translate('target.open_recharge'),
                    icon = "fas fa-plug",
                    distance = 2.0,
                    event = "lc_fuel:interactWithElectricCharger",
                    entityId = 0, -- No entity ID for config-based chargers
                    pumpModel = chargerLocation.prop,
                    isElectric = true
                })
            end
        end
    end

    -- Remove existing electric points
    local removedCount = 0
    for i = #interactionPoints, 1, -1 do
        if interactionPoints[i] and interactionPoints[i].isElectric then
            table.remove(interactionPoints, i)
            removedCount = removedCount + 1
        end
    end

    -- Add new electric points
    for _, point in ipairs(electricPoints) do
        table.insert(interactionPoints, point)
    end
end

-- Variables to track registered interaction points
local pumpRegistered = {}

-- Function to register gas pump interaction points
function registerInteractionPoints()
    -- Only register if not already registering (prevent duplicates)
    if isRegistering then
        return
    end

    isRegistering = true

    -- Create a table to track which points we've registered in this run
    local currentlyRegistered = {}
    local newRegistrations = 0
    local skippedRegistrations = 0

    -- First, remove all existing gas pump interaction points to prevent duplicates
    for interactId in pairs(registeredInteractIds) do
        if registeredInteractIds[interactId] == "pump" then
            exports.interact:removeCoords(interactId)
        end
    end

    -- Clear the tracking table
    for k, v in pairs(registeredInteractIds) do
        if v == "pump" then
            registeredInteractIds[k] = nil
        end
    end

    -- Reset pump registration tracking
    pumpRegistered = {}

    -- Register all gas pump interaction points
    for _, point in ipairs(interactionPoints) do
        -- Skip electric points - they'll be handled separately
        if point.isElectric then
            goto continue
        end

        -- Create a unique ID for this point
        local pointId = createUniquePointId('gas_pump_', point.entityId, point.coords)

        -- Track that we're registering this point
        currentlyRegistered[pointId] = true

        -- Create the ID that interact uses
        local interactId = coordsToInteractId(point.coords)

        -- Skip if already registered in this run
        if pumpRegistered[pointId] then
            skippedRegistrations = skippedRegistrations + 1
            goto continue
        end

        -- Add to our tracking
        pumpRegistered[pointId] = true
        registeredInteractIds[interactId] = "pump"
        newRegistrations = newRegistrations + 1

        -- Register the interaction point
        exports.interact:addCoords(point.coords, {
            label = point.label,
            name = 'lc_fuel_pump_' .. point.entityId,
            icon = point.icon,
            distance = point.distance,
            onSelect = function(data)
                if point.event then
                    TriggerEvent(point.event, {
                        entityId = point.entityId,
                        pumpModel = point.pumpModel,
                        isElectric = point.isElectric
                    })
                end
            end,
            canInteract = function(entity, distance, coords, name)
                -- Don't show interaction if player has nozzle in hand
                if DoesEntityExist(fuelNozzle) then
                    return false
                end

                -- Only show the closest interaction point if multiple are available
                local playerPos = GetEntityCoords(PlayerPedId())
                if not isClosestInteractionPoint(point, playerPos, interactionPoints) then
                    return false
                end

                return true -- Distance check is already done in isClosestInteractionPoint
            end
        })

        ::continue::
    end

    isRegistering = false
end

-- Variable to track registered electric charger points
local electricChargerRegistered = {}

-- Function to register electric charger interaction points
function registerElectricInteractionPoints()
    -- Only register if not already registering (prevent duplicates)
    if isRegistering then
        return
    end

    isRegistering = true

    -- Create a table to track which points we've registered in this run
    local currentlyRegistered = {}
    local newRegistrations = 0
    local skippedRegistrations = 0

    -- First, remove all existing electric charger interaction points to prevent duplicates
    for interactId in pairs(registeredInteractIds) do
        if registeredInteractIds[interactId] == "electric" then
            exports.interact:removeCoords(interactId)
        end
    end

    -- Clear the tracking table
    for k, v in pairs(registeredInteractIds) do
        if v == "electric" then
            registeredInteractIds[k] = nil
        end
    end

    -- Reset electric charger registration tracking
    electricChargerRegistered = {}

    -- Register all electric charger interaction points
    for _, point in ipairs(interactionPoints) do
        -- Skip gas pump points - they're handled separately
        if not point.isElectric then
            goto continue
        end

        -- Create a unique ID for this point
        local pointId = createUniquePointId('electric_charger_', point.entityId, point.coords)

        -- Track that we're registering this point
        currentlyRegistered[pointId] = true

        -- Create the ID that interact uses
        local interactId = coordsToInteractId(point.coords)

        -- Skip if already registered in this run
        if electricChargerRegistered[pointId] then
            skippedRegistrations = skippedRegistrations + 1
            goto continue
        end

        -- Add to our tracking
        electricChargerRegistered[pointId] = true
        registeredInteractIds[interactId] = "electric"
        newRegistrations = newRegistrations + 1

        -- Register the interaction point
        exports.interact:addCoords(point.coords, {
            label = point.label,
            name = 'lc_fuel_electric_' .. point.entityId,
            icon = point.icon,
            distance = point.distance,
            onSelect = function(data)
                if point.event then
                    TriggerEvent(point.event, {
                        entityId = point.entityId,
                        pumpModel = point.pumpModel,
                        isElectric = point.isElectric
                    })
                end
            end,
            canInteract = function(entity, distance, coords, name)
                -- Don't show interaction if player has nozzle in hand
                if DoesEntityExist(fuelNozzle) then
                    return false
                end

                -- Only show the closest interaction point if multiple are available
                local playerPos = GetEntityCoords(PlayerPedId())
                if not isClosestInteractionPoint(point, playerPos, interactionPoints) then
                    return false
                end

                return true -- Distance check is already done in isClosestInteractionPoint
            end
        })

        ::continue::
    end

    isRegistering = false
end

-- Variable to track if vehicle refueling interaction is registered
local vehicleRefuelingRegistered = false

-- Function to register vehicle refueling interaction
function registerVehicleRefuelingInteraction()
    -- Always re-register to ensure it's up to date

    -- Remove existing vehicle interactions first
    exports.interact:removeGlobalVehicle('lc_fuel_start_refuel')
    exports.interact:removeGlobalVehicle('lc_fuel_stop_refuel')

    -- Use the interact resource to add interaction to all vehicles
    exports.interact:addGlobalVehicle({
        label = Utils.translate('target.start_refuel'),
        name = 'lc_fuel_start_refuel',
        icon = "fas fa-gas-pump",
        distance = 2.0,
        onSelect = function(data)
            TriggerEvent("lc_fuel:interactWithVehicle", data)
        end,
        canInteract = function(entity, distance, coords, name)
            local ped = PlayerPedId()

            -- Only show if player has nozzle or jerry can and is near a vehicle
            if not DoesEntityExist(fuelNozzle) and GetSelectedPedWeapon(ped) ~= JERRY_CAN_HASH then
                return false
            end

            -- Don't show if already refueling
            if isRefuelling then
                return false
            end

            -- Don't show if any vehicle already has nozzle attached
            if vehicleAttachedToNozzle then
                -- Only allow interaction with the vehicle that has the nozzle attached
                if vehicleAttachedToNozzle ~= entity then
                    return false
                end
            end

            -- If using jerry can, make sure it has fuel
            if GetSelectedPedWeapon(ped) == JERRY_CAN_HASH and getJerryCanAmmo() <= 0 then
                return false
            end

            return distance < 2.0
        end
    })

    -- Interaction to stop refueling
    exports.interact:addGlobalVehicle({
        label = Utils.translate('target.stop_refuel'),
        name = 'lc_fuel_stop_refuel',
        icon = "fas fa-gas-pump",
        distance = 2.0,
        onSelect = function(data)
            TriggerEvent("lc_fuel:stopRefueling", data)
        end,
        canInteract = function(entity, distance, coords, name)
            -- Only show if currently refueling this vehicle
            if not isRefuelling or not vehicleAttachedToNozzle then
                return false
            end

            -- Check if it's the vehicle being refueled
            if vehicleAttachedToNozzle ~= entity then
                return false
            end

            return distance < 2.0
        end
    })

    vehicleRefuelingRegistered = true
end

-- Variable to track registered nozzle return points
local nozzleReturnRegistered = {}

-- Function to clean up all registered interactions
function cleanupAllInteractions()
    -- Reset all tracking variables
    pumpRegistered = {}
    electricChargerRegistered = {}
    nozzleReturnRegistered = {}

    -- If interact is available, remove all registered interactions
    if GetResourceState('interact') == 'started' then
        -- Remove all registered interactions by type
        for interactId, interactType in pairs(registeredInteractIds) do
            exports.interact:removeCoords(interactId)
        end

        -- Clear the tracking table
        registeredInteractIds = {}

        -- Remove vehicle interactions
        exports.interact:removeGlobalVehicle('lc_fuel_start_refuel')
        exports.interact:removeGlobalVehicle('lc_fuel_stop_refuel')
        exports.interact:removeGlobalVehicle('lc_fuel_return_nozzle')
    end
end

-- Function to register nozzle return interaction
function registerNozzleReturnInteraction()
    -- Only register if not already registering (prevent duplicates)
    if isRegistering then
        return
    end

    isRegistering = true

    -- First, remove any existing return nozzle interactions from vehicles
    exports.interact:removeGlobalVehicle('lc_fuel_return_nozzle')

    -- First, remove all existing nozzle return points to prevent duplicates
    for interactId in pairs(registeredInteractIds) do
        if registeredInteractIds[interactId] == "nozzle_return" then
            exports.interact:removeCoords(interactId)
        end
    end

    -- Clear the tracking table
    for k, v in pairs(registeredInteractIds) do
        if v == "nozzle_return" then
            registeredInteractIds[k] = nil
        end
    end

    -- Reset nozzle return registration tracking
    nozzleReturnRegistered = {}

    -- Create a table to track which points we've registered in this run
    local newRegistrations = 0
    local skippedRegistrations = 0

    -- Add interaction points for gas pumps and electric chargers
    for _, point in ipairs(interactionPoints) do
        -- Create unique IDs for this point
        local pointId = createUniquePointId('nozzle_return_', point.entityId, point.coords)

        -- Create the ID that interact uses
        local interactId = coordsToInteractId(point.coords)

        -- Skip if already registered in this run
        if nozzleReturnRegistered[pointId] then
            skippedRegistrations = skippedRegistrations + 1
            goto continue
        end

        -- Add to our tracking
        nozzleReturnRegistered[pointId] = true
        registeredInteractIds[interactId] = "nozzle_return"
        newRegistrations = newRegistrations + 1

        if not point.isElectric then
            -- Gas pump return nozzle
            exports.interact:addCoords(point.coords, {
                label = Utils.translate('target.return_nozzle'),
                name = 'lc_fuel_return_nozzle_gas_' .. point.entityId,
                icon = "fas fa-gas-pump",
                distance = 2.0,
                onSelect = function()
                    TriggerEvent("lc_fuel:returnNozzle", {
                        entityId = point.entityId
                    })
                end,
                canInteract = function(entity)
                    -- Only show if player has nozzle
                    if not DoesEntityExist(fuelNozzle) then
                        return false
                    end

                    -- Don't show if currently refueling
                    if isRefuelling then
                        return false
                    end

                    -- Don't show if entity is a vehicle
                    if entity ~= nil and IsEntityAVehicle(entity) then
                        return false
                    end

                    -- Only show when player is near the pump, not near vehicles
                    local playerPos = GetEntityCoords(PlayerPedId())
                    local distanceToPoint = #(playerPos - point.coords)
                    if distanceToPoint > 2.5 then
                        return false
                    end

                    -- Check if this is the pump that the nozzle came from
                    -- For config-based pumps (entityId = 0), we don't check this
                    if point.entityId ~= 0 and currentPump ~= point.entityId then
                        return false
                    end

                    return true
                end
            })
        else
            -- Electric charger return nozzle
            exports.interact:addCoords(point.coords, {
                label = Utils.translate('target.return_nozzle'),
                name = 'lc_fuel_return_nozzle_electric_' .. point.entityId,
                icon = "fas fa-plug",
                distance = 2.0,
                onSelect = function()
                    TriggerEvent("lc_fuel:returnNozzle", {
                        entityId = point.entityId
                    })
                end,
                canInteract = function(entity)
                    -- Only show if player has nozzle
                    if not DoesEntityExist(fuelNozzle) then
                        return false
                    end

                    -- Don't show if currently refueling
                    if isRefuelling then
                        return false
                    end

                    -- Don't show if entity is a vehicle
                    if entity ~= nil and IsEntityAVehicle(entity) then
                        return false
                    end

                    -- Only show when player is near the charger, not near vehicles
                    local playerPos = GetEntityCoords(PlayerPedId())
                    local distanceToPoint = #(playerPos - point.coords)
                    if distanceToPoint > 2.5 then
                        return false
                    end

                    -- Check if this is the charger that the nozzle came from
                    -- For config-based chargers (entityId = 0), we don't check this
                    if point.entityId ~= 0 and currentPump ~= point.entityId then
                        return false
                    end

                    return true
                end
            })
        end

        ::continue::
    end

    isRegistering = false
end

-- Event handlers for the interaction system
RegisterNetEvent("lc_fuel:interactWithGasPump")
AddEventHandler("lc_fuel:interactWithGasPump", function(data)
    local pump = data.entityId
    local pumpModel = data.pumpModel

    if pump and DoesEntityExist(pump) then
        clientOpenUI(pump, pumpModel, false)
    else
        exports['lc_utils']:notify("error", Utils.translate("pump_not_found"))
    end
end)

RegisterNetEvent("lc_fuel:interactWithElectricCharger")
AddEventHandler("lc_fuel:interactWithElectricCharger", function(data)
    local pump = data.entityId
    local pumpModel = data.pumpModel

    if (pump and DoesEntityExist(pump)) or pump == 0 then
        -- Allow interaction with config-based chargers (entityId = 0)
        clientOpenUI(pump, pumpModel, true)
    else
        exports['lc_utils']:notify("error", Utils.translate("pump_not_found"))
    end
end)

RegisterNetEvent("lc_fuel:interactWithVehicle")
AddEventHandler("lc_fuel:interactWithVehicle", function(data)
    local vehicle = data.entity

    if vehicle and DoesEntityExist(vehicle) and IsEntityAVehicle(vehicle) then
        -- Load variables to open the UI
        loadNuiVariables()

        local ped = PlayerPedId()

        -- Calculate if player is holding a jerry can
        local isFromJerryCan = false
        if not IsPedInAnyVehicle(ped, false) and GetSelectedPedWeapon(ped) == JERRY_CAN_HASH then
            isFromJerryCan = true
            remainingFuelToRefuel = getJerryCanAmmo()

            -- Set default fuel type for jerry can
            if not currentFuelTypePurchased then
                currentFuelTypePurchased = "regular"
            end
        end

        -- Get vehicle parameters
        local vehicleHash = GetEntityModel(vehicle)
        local vehicleParams = Config.CustomVehicleParameters[GetDisplayNameFromVehicleModel(vehicleHash):lower()] or Config.CustomVehicleParameters["default"]
        local vehicleCapPos = GetWorldPositionOfEntityBone(vehicle, GetEntityBoneIndexByName(vehicle, "petrolcap") ~= -1 and GetEntityBoneIndexByName(vehicle, "petrolcap") or GetEntityBoneIndexByName(vehicle, "petroltank"))

        if not vehicleCapPos or vehicleCapPos.x == 0.0 then
            -- If no petrol cap bone found, use a position relative to the vehicle
            vehicleCapPos = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, -1.5, 0.0)
        end

        -- Execute refuel action
        executeRefuelAction(isFromJerryCan, vehicle, vehicleCapPos, vehicleHash, vehicleParams)
    end
end)

RegisterNetEvent("lc_fuel:stopRefueling")
AddEventHandler("lc_fuel:stopRefueling", function(data)
    stopRefuelAction()
end)

RegisterNetEvent("lc_fuel:returnNozzle")
AddEventHandler("lc_fuel:returnNozzle", function(data)
    returnNozzle()
end)

-- Event to refresh interaction points
RegisterNetEvent("lc_fuel:refreshInteractionPoints")
AddEventHandler("lc_fuel:refreshInteractionPoints", function()
    -- Clean up all registered interactions first
    cleanupAllInteractions()

    -- Wait a bit to ensure cleanup is complete
    Wait(100)

    -- Create and register electric charger interaction points
    createElectricInteractionPoints()
    registerElectricInteractionPoints()

    -- Create and register gas pump interaction points
    createGasInteractionPoints()
    registerInteractionPoints()

    -- Register nozzle return interaction
    registerNozzleReturnInteraction()

    -- Register vehicle refueling interaction
    registerVehicleRefuelingInteraction()
end)

-- This function is now defined above

-- Initialize the interaction system
CreateThread(function()
    -- Wait for the resource to fully initialize
    Wait(3000)

    -- Check if the interact resource is available
    if GetResourceState('interact') ~= 'started' then
        return
    end

    if Config.InteractionSystem.enabled and Config.InteractionSystem.target == "disabled" then
        -- Create a thread that periodically updates interaction points
        CreateThread(function()
            -- Initial setup
            createGasInteractionPoints()
            createElectricInteractionPoints()
            registerInteractionPoints()
            registerVehicleRefuelingInteraction()
            registerNozzleReturnInteraction()

            -- Variable to track player position for optimization
            local lastPlayerPos = vector3(0, 0, 0)
            local lastUpdateTime = GetGameTimer()
            local lastResetTime = GetGameTimer()
            local lastVehicleUpdateTime = GetGameTimer()
            local lastNozzleUpdateTime = GetGameTimer()
            local lastElectricUpdateTime = GetGameTimer()
            local forceUpdate = false
            local updateCount = 0

            -- Initial setup - wait a bit to ensure everything is loaded
            Wait(5000)

            -- Initial cleanup to ensure we start fresh
            cleanupAllInteractions()

            -- Register vehicle refueling interaction once at startup
            registerVehicleRefuelingInteraction()

            -- Create and register electric charger interaction points at startup
            createElectricInteractionPoints()
            registerElectricInteractionPoints()

            while true do
                -- Get current player position
                local playerPed = PlayerPedId()
                local currentPlayerPos = GetEntityCoords(playerPed)
                local currentTime = GetGameTimer()

                -- Reset registration tracking every 30 minutes to prevent stale interactions
                if (currentTime - lastResetTime) > 1800000 then
                    -- Clean up all registered interactions
                    cleanupAllInteractions()
                    lastResetTime = currentTime
                    forceUpdate = true

                    -- Wait a bit after cleanup
                    Wait(1000)
                end

                -- Force update every 5 minutes regardless of movement
                if (currentTime - lastUpdateTime) > 300000 then
                    forceUpdate = true
                end

                -- Only update interaction points if player has moved more than 30 meters or force update
                if forceUpdate or #(lastPlayerPos - currentPlayerPos) > 30.0 then
                    -- Create interaction points for gas pumps
                    createGasInteractionPoints()

                    -- Register gas pump interaction points
                    registerInteractionPoints()

                    -- Update tracking variables
                    lastPlayerPos = currentPlayerPos
                    lastUpdateTime = currentTime
                    forceUpdate = false

                    -- Increment update counter
                    updateCount = updateCount + 1

                    -- Wait longer after an update to prevent rapid updates
                    Wait(5000)
                end

                -- Update electric charger interaction points every 5 minutes
                if (currentTime - lastElectricUpdateTime) > 300000 then
                    -- Create interaction points for electric chargers
                    createElectricInteractionPoints()

                    -- Register electric charger interaction points
                    registerElectricInteractionPoints()

                    lastElectricUpdateTime = currentTime

                    -- Wait a bit after update
                    Wait(1000)
                end

                -- Update vehicle refueling interaction every 10 minutes
                if (currentTime - lastVehicleUpdateTime) > 600000 then
                    registerVehicleRefuelingInteraction()
                    lastVehicleUpdateTime = currentTime

                    -- Wait a bit after update
                    Wait(1000)
                end

                -- Update nozzle return interaction every 10 minutes
                if (currentTime - lastNozzleUpdateTime) > 600000 then
                    registerNozzleReturnInteraction()
                    lastNozzleUpdateTime = currentTime

                    -- Wait a bit after update
                    Wait(1000)
                end

                -- Wait 5 seconds before checking again if player hasn't moved much
                Wait(5000)
            end
        end)

        -- Load electric chargers if enabled
        if Config.Electric.enabled then
            -- Create electric zones to load chargers
            createElectricZones()

            -- Load all electric chargers
            for _, charger in pairs(Config.Electric.chargersLocation) do
                loadElectricCharger(charger)
            end
        end
    end
end)
